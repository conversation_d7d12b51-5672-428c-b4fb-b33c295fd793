<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Klicktape</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #000000, #1a1a1a, #2a2a2a);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 400px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 20px;
        }
        .message {
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #FFD700;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #ff6b6b;
            margin-top: 20px;
        }
        .manual-link {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 24px;
            background: #FFD700;
            color: #000;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
        }
        .form-container {
            display: none;
            max-width: 400px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #FFD700;
            font-weight: bold;
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-input:focus {
            outline: none;
            border-color: #FFD700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }
        .form-button {
            width: 100%;
            padding: 12px;
            background: #FFD700;
            color: #000;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .form-button:hover {
            background: #FFC700;
        }
        .form-button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .success {
            color: #4CAF50;
            margin-top: 20px;
        }
        .password-requirements {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 5px;
        }
        .debug-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 12px;
            text-align: left;
            overflow-wrap: break-word;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">Klicktape</div>

        <!-- Loading State -->
        <div id="loading-state">
            <div class="message">
                Verifying your password reset link...
            </div>
            <div class="spinner"></div>
        </div>

        <!-- Password Reset Form -->
        <div id="reset-form" class="form-container">
            <div class="message">
                Reset Your Password
            </div>
            <form id="password-form">
                <div class="form-group">
                    <label class="form-label" for="new-password">New Password</label>
                    <input
                        type="password"
                        id="new-password"
                        class="form-input"
                        placeholder="Enter your new password"
                        required
                        minlength="8"
                    >
                    <div class="password-requirements">
                        Password must be at least 8 characters long
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="confirm-password">Confirm Password</label>
                    <input
                        type="password"
                        id="confirm-password"
                        class="form-input"
                        placeholder="Confirm your new password"
                        required
                        minlength="8"
                    >
                </div>
                <button type="submit" class="form-button" id="submit-btn">
                    Reset Password
                </button>
            </form>
        </div>

        <!-- Success State -->
        <div id="success-state" class="form-container">
            <div class="message success">
                ✅ Password Reset Successfully!
            </div>
            <p style="margin: 20px 0; line-height: 1.5;">
                Your password has been updated successfully.
                <br><br>
                Please return to the Klicktape app and log in with your new password.
            </p>
            <div style="background: rgba(255, 215, 0, 0.1); padding: 15px; border-radius: 8px; margin-top: 20px;">
                <strong style="color: #FFD700;">Next Steps:</strong>
                <br>
                1. Open the Klicktape app on your device
                <br>
                2. Use your email and new password to log in
            </div>
        </div>

        <!-- Error State -->
        <div id="error-message" class="error" style="display: none;">
            <div class="message">
                Password Reset Failed
            </div>
            <p id="error-text">Something went wrong. Please try again or request a new password reset link.</p>
        </div>

        <!-- Debug Info (remove in production) -->
        <div id="debug-info" class="debug-info" style="display: none;">
            <strong>Debug Info:</strong>
            <div id="debug-content"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script>
        // Initialize Supabase client
        const { createClient } = supabase;
        const supabaseClient = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey);

        let currentSession = null;

        function getUrlParams() {
            const hash = window.location.hash;
            const hashParts = hash.split('#').filter(part => part.length > 0);
            
            console.log('Hash parts:', hashParts);
            
            // Look for the hash that contains a JWT (starts with "ey")
            let supabaseHash = '';
            let fallbackHash = '';
            
            for (const part of hashParts) {
                const params = new URLSearchParams(part);
                const accessToken = params.get('access_token');
                
                if (accessToken && accessToken.startsWith('ey')) {
                    // This looks like a real JWT token
                    supabaseHash = part;
                    break;
                } else if (accessToken) {
                    // This might be a fallback/test token
                    fallbackHash = part;
                }
            }
            
            // Use the real Supabase hash if found, otherwise use fallback
            const hashToUse = supabaseHash || fallbackHash || hashParts[hashParts.length - 1] || '';
            
            console.log('Using hash:', hashToUse);
            
            const hashParams = new URLSearchParams(hashToUse);
            const queryParams = new URLSearchParams(window.location.search);

            const params = {
                access_token: hashParams.get('access_token') || queryParams.get('access_token'),
                refresh_token: hashParams.get('refresh_token') || queryParams.get('refresh_token'),
                error: hashParams.get('error') || queryParams.get('error'),
                error_description: hashParams.get('error_description') || queryParams.get('error_description'),
                type: hashParams.get('type') || queryParams.get('type'),
                expires_at: hashParams.get('expires_at') || queryParams.get('expires_at'),
                expires_in: hashParams.get('expires_in') || queryParams.get('expires_in'),
                token_type: hashParams.get('token_type') || queryParams.get('token_type')
            };

            console.log('Parsed params:', params);
            
            // Debug info
            const debugInfo = document.getElementById('debug-content');
            if (debugInfo) {
                debugInfo.innerHTML = `
                    <div><strong>Full URL:</strong> ${window.location.href}</div>
                    <div><strong>Hash parts count:</strong> ${hashParts.length}</div>
                    <div><strong>Selected hash:</strong> ${hashToUse.substring(0, 100)}${hashToUse.length > 100 ? '...' : ''}</div>
                    <div><strong>Access token starts with:</strong> ${params.access_token ? params.access_token.substring(0, 20) + '...' : 'null'}</div>
                    <div><strong>Has refresh token:</strong> ${!!params.refresh_token}</div>
                    <div><strong>Token type:</strong> ${params.type}</div>
                `;
            }
            
            return params;
        }

        function showError(message) {
            document.getElementById('loading-state').style.display = 'none';
            document.getElementById('reset-form').style.display = 'none';
            document.getElementById('success-state').style.display = 'none';
            document.getElementById('error-message').style.display = 'block';
            document.getElementById('error-text').textContent = message;
            document.getElementById('debug-info').style.display = 'block'; // Show debug info on error
        }

        function showForm() {
            document.getElementById('loading-state').style.display = 'none';
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-state').style.display = 'none';
            document.getElementById('reset-form').style.display = 'block';
            document.getElementById('debug-info').style.display = 'none';
        }

        function showSuccess() {
            document.getElementById('loading-state').style.display = 'none';
            document.getElementById('reset-form').style.display = 'none';
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-state').style.display = 'block';
            document.getElementById('debug-info').style.display = 'none';
        }

        async function verifySession() {
            const params = getUrlParams();
            console.log('URL params:', params);

            // Check for errors first
            if (params.error) {
                console.error('Auth error:', params.error, params.error_description);
                showError('Password reset link has expired or is invalid.');
                return;
            }

            // Check if we have access token
            if (!params.access_token) {
                console.error('No access token found in URL');
                showError('Invalid password reset link. No access token found.');
                return;
            }

            // Validate that the access token looks like a JWT
            if (!params.access_token.startsWith('ey')) {
                console.error('Access token does not appear to be a valid JWT');
                showError('Invalid access token format.');
                return;
            }

            try {
                // For password recovery, we might not always have a refresh token in the URL
                const sessionData = {
                    access_token: params.access_token,
                    refresh_token: params.refresh_token || null
                };

                console.log('Setting session with:', { 
                    hasAccessToken: !!sessionData.access_token,
                    hasRefreshToken: !!sessionData.refresh_token 
                });

                const { data, error } = await supabaseClient.auth.setSession(sessionData);

                if (error) {
                    console.error('Session error:', error);
                    showError(`Session error: ${error.message}`);
                    return;
                }

                if (data.session) {
                    currentSession = data.session;
                    console.log('Session established successfully');
                    showForm();
                } else {
                    console.error('No session returned from setSession');
                    showError('Unable to establish session. Please try again.');
                }
            } catch (error) {
                console.error('Error setting session:', error);
                showError(`Error: ${error.message}`);
            }
        }

        async function handlePasswordReset(event) {
            event.preventDefault();

            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const submitBtn = document.getElementById('submit-btn');

            // Validate passwords
            if (newPassword !== confirmPassword) {
                alert('Passwords do not match!');
                return;
            }

            if (newPassword.length < 8) {
                alert('Password must be at least 8 characters long!');
                return;
            }

            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.textContent = 'Resetting Password...';

            try {
                const { error } = await supabaseClient.auth.updateUser({
                    password: newPassword
                });

                if (error) {
                    console.error('Password update error:', error);
                    alert('Failed to update password: ' + error.message);
                    return;
                }

                console.log('Password updated successfully');
                showSuccess();

                // Sign out the user after successful password reset
                await supabaseClient.auth.signOut();

            } catch (error) {
                console.error('Error updating password:', error);
                alert('Something went wrong. Please try again.');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Reset Password';
            }
        }

        // Initialize when page loads
        window.onload = function() {
            verifySession();

            // Add form submit handler
            document.getElementById('password-form').addEventListener('submit', handlePasswordReset);

            // Add password confirmation validation
            document.getElementById('confirm-password').addEventListener('input', function() {
                const newPassword = document.getElementById('new-password').value;
                const confirmPassword = this.value;

                if (confirmPassword && newPassword !== confirmPassword) {
                    this.style.borderColor = '#ff6b6b';
                } else {
                    this.style.borderColor = 'rgba(255, 215, 0, 0.3)';
                }
            });
        };
    </script>
</body>
</html>